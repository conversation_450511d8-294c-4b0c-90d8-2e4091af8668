<!DOCTYPE html>
<html lang="en">
<head>    <meta charset="UTF-8">    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Area Configuration - Traffic Monitoring System</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/canvas-styles.css">
    <link rel="stylesheet" href="css/canvas-fix.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Traffic Monitoring System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sources.html">Video Sources</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="config.html">Configuration</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5>Area Configuration</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Select Video Source</label>
                                <select class="form-select" id="sourceSelector">
                                    <option value="">-- Select Video Source --</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Active Area Type</label>
                                <select class="form-select" id="areaTypeSelector">
                                    <option value="detection">Detection Area</option>
                                    <option value="speed">Speed Lines</option>
                                    <option value="wrong_direction">Wrong Direction Lines</option>
                                    <option value="parking">Parking Area</option>
                                    <option value="traffic_line">Traffic Line</option>
                                    <option value="traffic_sign">Traffic Sign Area</option>
                                    <option value="custom">Custom Area</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12 d-flex justify-content-between">
                                <div class="btn-group" role="group" id="drawControls">
                                    <button class="btn btn-outline-primary" id="btnStartPolygon">
                                        <i class="fas fa-draw-polygon"></i> Draw Polygon
                                    </button>
                                    <button class="btn btn-outline-primary" id="btnStartLine">
                                        <i class="fas fa-slash"></i> Draw Line
                                    </button>
                                    <button class="btn btn-outline-danger" id="btnClear">
                                        <i class="fas fa-trash"></i> Clear All
                                    </button>
                                </div>
                                <div class="btn-group">
                                    <button class="btn btn-success" id="btnSaveConfig">
                                        <i class="fas fa-save"></i> Save Configuration
                                    </button>
                                    <button class="btn btn-info" id="btnToggleControls">
                                        <i class="fas fa-cog"></i> Show Controls
                                    </button>
                                </div>
                            </div>
                        </div>                        <div id="playerControls" class="row mb-3 bg-light p-2 rounded" style="display: none;">
                            <div class="col-12">
                                <h5 class="mb-3">Detection Models</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-check form-switch d-flex align-items-center mb-2">
                                            <input class="form-check-input me-2" type="checkbox" id="toggleAccident" checked>
                                            <span class="form-check-label">Accident Detection</span>
                                        </label>
                                        <label class="form-check form-switch d-flex align-items-center mb-2">
                                            <input class="form-check-input me-2" type="checkbox" id="toggleHelmet" checked>
                                            <span class="form-check-label">Helmet Detection</span>
                                        </label>
                                        <label class="form-check form-switch d-flex align-items-center mb-2">
                                            <input class="form-check-input me-2" type="checkbox" id="toggleTraffic" checked>
                                            <span class="form-check-label">Traffic Violation</span>
                                        </label>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-check form-switch d-flex align-items-center mb-2">
                                            <input class="form-check-input me-2" type="checkbox" id="toggleSpeed" checked>
                                            <span class="form-check-label">Speed Detection</span>
                                        </label>
                                        <label class="form-check form-switch d-flex align-items-center mb-2">
                                            <input class="form-check-input me-2" type="checkbox" id="toggleParking" checked>
                                            <span class="form-check-label">Parking Detection</span>
                                        </label>
                                        <label class="form-check form-switch d-flex align-items-center mb-2">
                                            <input class="form-check-input me-2" type="checkbox" id="toggleWrongDir" checked>
                                            <span class="form-check-label">Wrong Direction</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-primary btn-sm" id="btnApplySettings">Apply Settings</button>
                                </div>
                            </div>
                        </div>                        <div class="row">
                            <div class="col-md-12">                                <div class="canvas-container" style="background-color: #000;">
                                    <canvas id="videoCanvas" style="border: 1px solid #ccc;"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <strong>Instructions:</strong>
                                    <ol>
                                        <li>Select a video source from the dropdown menu</li>
                                        <li>Select the area type you want to configure</li>
                                        <li>Click "Draw Polygon" to create an area or "Draw Line" for line-based areas</li>
                                        <li>Click on the canvas to add points to your polygon/line</li>
                                        <li>Double-click to complete your shape</li>
                                        <li>Click "Save Configuration" to save your changes</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fabric@5.3.0/dist/fabric.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/area-config.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize area configuration page
            loadVideoSources();
            setupAreaConfigUI();
        });
    </script>
</body>
</html>
