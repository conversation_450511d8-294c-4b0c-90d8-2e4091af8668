/* Main styles for Traffic Monitoring System */

body {
    background-color: #f5f5f5;
}

.navbar-brand {
    font-weight: 700;
}

.card {
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: 600;
}

/* Source card styles */
.source-card {
    transition: all 0.3s ease;
}

.source-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.source-status {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 15px;
    height: 15px;
    border-radius: 50%;
}

.status-running {
    background-color: #28a745;
}

.status-stopped {
    background-color: #dc3545;
}

.status-error {
    background-color: #ffc107;
}

/* Canvas styles */
.canvas-container {
    position: relative;
    margin: 0 auto;
    background-color: #000;
    overflow: hidden;
}

/* Area drawing colors */
.area-detection {
    stroke: rgb(0, 255, 0);
    fill: rgba(0, 255, 0, 0.2);
}

.area-speed {
    stroke: rgb(255, 0, 0);
    fill: rgba(255, 0, 0, 0.2);
}

.area-wrong_direction {
    stroke: rgb(0, 0, 255);
    fill: rgba(0, 0, 255, 0.2);
}

.area-parking {
    stroke: rgb(255, 255, 0);
    fill: rgba(255, 255, 0, 0.2);
}

.area-traffic_line {
    stroke: rgb(255, 0, 255);
    fill: rgba(255, 0, 255, 0.2);
}

.area-traffic_sign {
    stroke: rgb(0, 255, 255);
    fill: rgba(0, 255, 255, 0.2);
}

.area-custom {
    stroke: rgb(128, 128, 128);
    fill: rgba(128, 128, 128, 0.2);
}

/* Line styles */
.line-speed, .line-wrong_direction, .line-traffic_line {
    stroke-width: 2;
}
