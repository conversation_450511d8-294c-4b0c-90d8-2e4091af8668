// area-config.js - Area configuration functionality for Traffic Monitoring System

// Global variables
let canvas = null;
let fabricCanvas = null;
let selectedSource = null;
let currentAreaType = 'detection';
let areaColors = {
    'detection': { color: 'rgb(0, 255, 0)', fillColor: 'rgba(0, 255, 0, 0.2)' },
    'speed': { color: 'rgb(255, 0, 0)', fillColor: 'rgba(255, 0, 0, 0.2)' },
    'wrong_direction': { color: 'rgb(0, 0, 255)', fillColor: 'rgba(0, 0, 255, 0.2)' },
    'parking': { color: 'rgb(255, 255, 0)', fillColor: 'rgba(255, 255, 0, 0.2)' },
    'traffic_line': { color: 'rgb(255, 0, 255)', fillColor: 'rgba(255, 0, 255, 0.2)' },
    'traffic_sign': { color: 'rgb(0, 255, 255)', fillColor: 'rgba(0, 255, 255, 0.2)' },
    'custom': { color: 'rgb(128, 128, 128)', fillColor: 'rgba(128, 128, 128, 0.2)' }
};
let videoElement = null;
let videoStream = null;
let isDrawing = false;
let currentShape = null;
let linePoints = [];
let polygonPoints = [];
let existingAreas = {};
let currentFrameWidth = 0;
let currentFrameHeight = 0;
const DEFAULT_FRAME_WIDTH = 1920; // Default width, adjust as needed
const DEFAULT_FRAME_HEIGHT = 1080; // Default height, adjust as needed
let toastTimeout = null;

// Setup the area configuration UI
function setupAreaConfigUI() {
    // Get query parameters
    const urlParams = new URLSearchParams(window.location.search);
    const sourceId = urlParams.get('source');
    
    // Set up canvas
    canvas = document.getElementById('videoCanvas');
    
    // Set canvas container to have appropriate dimensions
    const container = canvas.parentElement;
    
    // Initialize fabric canvas
    fabricCanvas = new fabric.Canvas('videoCanvas', {
        selection: false,
        preserveObjectStacking: true
    });
    
    // Force canvas to initialize with appropriate dimensions
    fabricCanvas.setWidth(1280);
    fabricCanvas.setHeight(720);
    
    // Adjust canvas size initially and on window resize
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Setup event listeners
    setupEventListeners();
    
    // If source ID is provided in URL, select it
    if (sourceId) {
        // Wait for sources to load
        setTimeout(() => {
            const sourceSelector = document.getElementById('sourceSelector');
            if (sourceSelector) {
                sourceSelector.value = sourceId;
                sourceSelector.dispatchEvent(new Event('change'));
            }
        }, 1000);
    }
    
    // Create toast container for notifications if it doesn't exist
    if (!document.getElementById('toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = 1050;
        document.body.appendChild(toastContainer);
    }
}

// Show toast notification
function showToast(message, type = 'info') {
    // Clear any existing toast
    if (toastTimeout) {
        clearTimeout(toastTimeout);
    }
    
    // Remove existing toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // Create toast
    const toastContainer = document.getElementById('toast-container');
    
    // Set toast class based on type
    let bgClass = 'bg-info';
    if (type === 'success') bgClass = 'bg-success';
    if (type === 'error') bgClass = 'bg-danger';
    if (type === 'warning') bgClass = 'bg-warning';
    
    const toastHtml = `
        <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header ${bgClass} text-white">
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.innerHTML = toastHtml;
    
    // Add close functionality
    const closeBtn = toastContainer.querySelector('.btn-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            const toast = document.querySelector('.toast');
            if (toast) toast.remove();
        });
    }
    
    // Auto-hide after 3 seconds
    toastTimeout = setTimeout(() => {
        const toast = document.querySelector('.toast');
        if (toast) toast.remove();
    }, 3000);
}

// Load video sources for selector
async function loadVideoSources() {
    try {
        const response = await fetch(API_ENDPOINTS.SOURCES);
        const sources = await response.json();
        videoSources = sources;
        
        // Populate source selector
        const sourceSelector = document.getElementById('sourceSelector');
        sourceSelector.innerHTML = '<option value="">-- Select Video Source --</option>';
        
        sources.forEach(source => {
            const option = document.createElement('option');
            option.value = source.id;
            option.textContent = `${source.name} (${source.location})`;
            sourceSelector.appendChild(option);
        });
        
        return sources;
    } catch (error) {
        console.error('Error loading video sources:', error);
        return [];
    }
}

// Setup event listeners
function setupEventListeners() {
    // Source selector
    document.getElementById('sourceSelector').addEventListener('change', onSourceChange);
    
    // Area type selector
    document.getElementById('areaTypeSelector').addEventListener('change', onAreaTypeChange);
    
    // Drawing controls
    document.getElementById('btnStartPolygon').addEventListener('click', startDrawingPolygon);
    document.getElementById('btnStartLine').addEventListener('click', startDrawingLine);
    document.getElementById('btnClear').addEventListener('click', clearAreas);
    document.getElementById('btnSaveConfig').addEventListener('click', saveAreaConfiguration);
    document.getElementById('btnToggleControls').addEventListener('click', toggleControls);
    
    // Model toggle controls
    document.getElementById('btnApplySettings').addEventListener('click', saveModelSettings);
    
    // Initialize fabric.js event handlers
    initializeFabricEvents();
}

// Initialize fabric.js event handlers
function initializeFabricEvents() {
    // Mouse down event
    fabricCanvas.on('mouse:down', function(options) {
        if (!isDrawing) return;
        
        const pointer = fabricCanvas.getPointer(options.e);
        
        if (currentShape === 'polygon') {
            // First point of polygon
            if (polygonPoints.length === 0) {
                polygonPoints.push({ x: pointer.x, y: pointer.y });
                
                // Create circle for first point
                const circle = new fabric.Circle({
                    left: pointer.x - 5,
                    top: pointer.y - 5,
                    radius: 5,
                    fill: areaColors[currentAreaType].color,
                    stroke: '#fff',
                    strokeWidth: 1,
                    originX: 'center',
                    originY: 'center',
                    selectable: false,
                    hasBorders: false,
                    hasControls: false
                });
                
                fabricCanvas.add(circle);
                fabricCanvas.renderAll();
            } 
            // Subsequent points
            else {
                polygonPoints.push({ x: pointer.x, y: pointer.y });
                
                // Create circle for this point
                const circle = new fabric.Circle({
                    left: pointer.x - 5,
                    top: pointer.y - 5,
                    radius: 5,
                    fill: areaColors[currentAreaType].color,
                    stroke: '#fff',
                    strokeWidth: 1,
                    originX: 'center',
                    originY: 'center',
                    selectable: false,
                    hasBorders: false,
                    hasControls: false
                });
                
                fabricCanvas.add(circle);
                
                // Draw line from previous point
                if (polygonPoints.length > 1) {
                    const prevPoint = polygonPoints[polygonPoints.length - 2];
                    const line = new fabric.Line(
                        [prevPoint.x, prevPoint.y, pointer.x, pointer.y],
                        {
                            stroke: areaColors[currentAreaType].color,
                            strokeWidth: 2,
                            selectable: false,
                            hasBorders: false,
                            hasControls: false,
                            evented: false
                        }
                    );
                    
                    fabricCanvas.add(line);
                }
                
                fabricCanvas.renderAll();
            }
        } 
        // Line drawing
        else if (currentShape === 'line') {
            if (linePoints.length === 0) {
                linePoints.push({ x: pointer.x, y: pointer.y });
                
                // Create circle for first point
                const circle = new fabric.Circle({
                    left: pointer.x - 5,
                    top: pointer.y - 5,
                    radius: 5,
                    fill: areaColors[currentAreaType].color,
                    stroke: '#fff',
                    strokeWidth: 1,
                    originX: 'center',
                    originY: 'center',
                    selectable: false,
                    hasBorders: false,
                    hasControls: false
                });
                
                fabricCanvas.add(circle);
                fabricCanvas.renderAll();
            } 
            else if (linePoints.length === 1) {
                linePoints.push({ x: pointer.x, y: pointer.y });
                
                // Create circle for second point
                const circle = new fabric.Circle({
                    left: pointer.x - 5,
                    top: pointer.y - 5,
                    radius: 5,
                    fill: areaColors[currentAreaType].color,
                    stroke: '#fff',
                    strokeWidth: 1,
                    originX: 'center',
                    originY: 'center',
                    selectable: false,
                    hasBorders: false,
                    hasControls: false
                });
                
                fabricCanvas.add(circle);
                
                // Draw line connecting the two points
                const prevPoint = linePoints[0];
                const line = new fabric.Line(
                    [prevPoint.x, prevPoint.y, pointer.x, pointer.y],
                    {
                        stroke: areaColors[currentAreaType].color,
                        strokeWidth: 2,
                        selectable: false,
                        hasBorders: false,
                        hasControls: false,
                        evented: false
                    }
                );
                
                fabricCanvas.add(line);
                fabricCanvas.renderAll();
                
                // Save the line and stop drawing
                saveShape();
            }
        }
    });
    
    // Double click to complete polygon
    fabricCanvas.on('mouse:dblclick', function() {
        if (isDrawing && currentShape === 'polygon' && polygonPoints.length >= 3) {
            saveShape();
        }
    });
    
    // Mouse move to show preview
    fabricCanvas.on('mouse:move', function(options) {
        if (!isDrawing) return;
        
        const pointer = fabricCanvas.getPointer(options.e);
        
        if (currentShape === 'polygon' && polygonPoints.length > 0) {
            // Remove the preview line if it exists
            fabricCanvas.getObjects().forEach(obj => {
                if (obj.isPreviewLine) {
                    fabricCanvas.remove(obj);
                }
            });
            
            // Draw preview line
            const lastPoint = polygonPoints[polygonPoints.length - 1];
            const previewLine = new fabric.Line(
                [lastPoint.x, lastPoint.y, pointer.x, pointer.y],
                {
                    stroke: areaColors[currentAreaType].color,
                    strokeWidth: 2,
                    strokeDashArray: [5, 5],
                    selectable: false,
                    hasBorders: false,
                    hasControls: false,
                    evented: false,
                    isPreviewLine: true
                }
            );
            
            fabricCanvas.add(previewLine);
            fabricCanvas.renderAll();
        }
    });
}

// Resize canvas to match container while maintaining video aspect ratio
function resizeCanvas() {
    // Get the container dimensions
    const canvasContainer = document.getElementById('canvas-container');
    const canvasWidth = canvasContainer.offsetWidth;
    const canvasHeight = canvasContainer.offsetHeight;
    
    // Calculate background image dimensions while maintaining aspect ratio
    const videoAspectRatio = window.origVideoWidth / window.origVideoHeight;
    let bgWidth = canvasWidth;
    let bgHeight = canvasWidth / videoAspectRatio;
    
    // Adjust if height exceeds container
    if (bgHeight > canvasHeight) {
        bgHeight = canvasHeight;
        bgWidth = canvasHeight * videoAspectRatio;
    }
    
    // Calculate scaling and offset
    const scaleX = bgWidth / window.origVideoWidth;
    const scaleY = bgHeight / window.origVideoHeight;
    const left = (canvasWidth - bgWidth) / 2;
    const top = (canvasHeight - bgHeight) / 2;
    
    // Store the background image info for coordinate conversions
    window.backgroundImageInfo = {
        scaleX: scaleX,
        scaleY: scaleY,
        left: left,
        top: top
    };
    console.log('resizeCanvas → backgroundImageInfo:', 
        window.backgroundImageInfo, 
        'origVideo:', {w: window.origVideoWidth, h: window.origVideoHeight},
        'scaled:', {w: bgWidth, h: bgHeight}
    );
    
    // Update canvas dimensions
    fabricCanvas.setWidth(canvasWidth);
    fabricCanvas.setHeight(canvasHeight);
    
    // Update background image position and scale
    if (fabricCanvas.backgroundImage) {
        fabricCanvas.backgroundImage.set({
            scaleX: scaleX,
            scaleY: scaleY,
            left: left,
            top: top
        });
    }
    
    // Render the canvas
    fabricCanvas.renderAll();
    
    // Redraw areas if they exist
    if (window.loadedAreas) {
        redrawAreas();
    }
}

// Handle source selection change
async function onSourceChange(e) {
    const sourceId = e.target.value;
    
    // Close any existing WebSocket connection
    if (videoStream) {
        videoStream.close();
        videoStream = null;
    }
    
    if (!sourceId) {
        selectedSource = null;
        clearCanvas();
        // Reset frame dimensions when no source is selected
        currentFrameWidth = 0;
        currentFrameHeight = 0;
        resizeCanvas(); // Resize to default or empty state
        return;
    }
    
    selectedSource = sourceId;
    
    // Find the source details
    const source = videoSources.find(s => s.id === sourceId);
    
    if (!source) {
        console.error('Source not found:', sourceId);
        return;
    }
    
    // Clear canvas and reset frame dimensions before loading new source
    clearCanvas(); // This also stops drawing and clears existingAreas
    currentFrameWidth = 0; // Reset before loading new video
    currentFrameHeight = 0;
    // resizeCanvas(); // Initial resize before video metadata is known, will use defaults

    // The order of operations will be: loadVideo (gets dimensions, then calls resizeCanvas),
    // then loadAreaConfiguration (which uses the new canvas size from resizeCanvas),
    // then loadModelSettings.
    await loadVideo(source); // loadVideo will call resizeCanvas after getting frame dimensions
    await loadAreaConfiguration(sourceId);
    await loadModelSettings(sourceId);
}

// Load video stream or file
async function loadVideo(source) {
    // Flag to track if this is a restart
    const isRestart = selectedSource === source.id && videoStream !== null;
    
    // Stop any existing stream
    if (videoStream) {
        videoStream.close();
        videoStream = null;
    }

    try {
        // First, try to get a frame to determine dimensions
        const response = await fetch(`${API_ENDPOINTS.SOURCES}/${source.id}/frame`);
        if (!response.ok) {
            throw new Error(`Failed to get frame: ${response.status}`);
        }

        const frameData = await response.json();
        currentFrameWidth = parseInt(frameData.width, 10) || DEFAULT_FRAME_WIDTH;
        currentFrameHeight = parseInt(frameData.height, 10) || DEFAULT_FRAME_HEIGHT;

        console.log(`Video dimensions: ${currentFrameWidth}x${currentFrameHeight}, isRestart: ${isRestart}`);

        // Update canvas size based on video aspect ratio
        resizeCanvas(); // Call resizeCanvas AFTER frame dimensions are known        // Create initial image for background
        const img = new Image();
        img.onload = function() {
            const imgWidth = img.width || currentFrameWidth;
            const imgHeight = img.height || currentFrameHeight;
            const imgAspectRatio = imgWidth / imgHeight;
            const canvasAspectRatio = fabricCanvas.width / fabricCanvas.height;
            
            let scaleX, scaleY, left = 0, top = 0;
            
            // Scale to completely fill the canvas while maintaining aspect ratio
            if (canvasAspectRatio > imgAspectRatio) {
                // Canvas is wider than image
                scaleX = fabricCanvas.width / imgWidth;
                scaleY = scaleX; // Keep aspect ratio
                // Center vertically
                top = (fabricCanvas.height - (imgHeight * scaleY)) / 2;
            } else {
                // Canvas is taller than image
                scaleY = fabricCanvas.height / imgHeight;
                scaleX = scaleY; // Keep aspect ratio
                // Center horizontally
                left = (fabricCanvas.width - (imgWidth * scaleX)) / 2;
            }
            
            console.log(`Initial background image: size=${imgWidth}x${imgHeight}, scale=${scaleX.toFixed(2)}, position=${left.toFixed(0)},${top.toFixed(0)}`);
            
            const fabricImage = new fabric.Image(img, {
                left: left,
                top: top,
                originX: 'left',
                originY: 'top',
                scaleX: scaleX,
                scaleY: scaleY
            });
            
            // Store original dimensions and scaling for coordinate transformations
            window.backgroundImageInfo = {
                width: imgWidth,
                height: imgHeight,
                scaleX: scaleX,
                scaleY: scaleY,
                left: left,
                top: top
            };
            
            fabricCanvas.setBackgroundImage(fabricImage, fabricCanvas.renderAll.bind(fabricCanvas));
        };
        img.src = "data:image/jpeg;base64," + frameData.frame_data;

        // Connect to WebSocket for streaming
        const wsUrl = `${window.location.protocol === 'https:' ? 'wss://' : 'ws://'}${window.location.host}/ws/video/${source.id}`;
        videoStream = new WebSocket(wsUrl);

        videoStream.onopen = function(event) {
            console.log(`Connected to video stream: ${source.id}`);
        };        videoStream.onmessage = function(event) {
            const data = JSON.parse(event.data);

            if (data.event === 'frame') {
                const newFrameWidth = parseInt(data.width, 10);
                const newFrameHeight = parseInt(data.height, 10);

                let dimensionsChanged = false;
                if (newFrameWidth && newFrameHeight && 
                    (newFrameWidth !== currentFrameWidth || newFrameHeight !== currentFrameHeight)) {
                    currentFrameWidth = newFrameWidth;
                    currentFrameHeight = newFrameHeight;
                    dimensionsChanged = true;
                    console.log(`Video dimensions changed to: ${newFrameWidth}x${newFrameHeight}`);
                }

                if (dimensionsChanged) {
                    resizeCanvas(); // This will also rescale the background if it exists
                    // If dimensions changed, reload areas to ensure they're properly scaled
                    if (Object.keys(existingAreas).length > 0) {
                        redrawAreas();
                    } else {
                        // If no areas are loaded yet, try loading them from the API
                        loadAreaConfiguration(selectedSource);
                    }
                }                // Update the background image
                const img = new Image();
                img.onload = function() {
                    const imgWidth = img.width || currentFrameWidth;
                    const imgHeight = img.height || currentFrameHeight;
                    const imgAspectRatio = imgWidth / imgHeight;
                    const canvasAspectRatio = fabricCanvas.width / fabricCanvas.height;
                    
                    let scaleX, scaleY, left = 0, top = 0;
                    
                    // Scale to completely fill the canvas while maintaining aspect ratio
                    if (canvasAspectRatio > imgAspectRatio) {
                        // Canvas is wider than image
                        scaleX = fabricCanvas.width / imgWidth;

                        // Scale to completely fill the canvas while maintaining aspect ratio
                        scaleY = scaleX; // Keep aspect ratio
                        // Center vertically
                        top = (fabricCanvas.height - (imgHeight * scaleY)) / 2;
                    } else {
                        // Canvas is taller than image
                        scaleY = fabricCanvas.height / imgHeight;

                        // Scale to completely fill the canvas while maintaining aspect ratio

                        
                        scaleX = scaleY; // Keep aspect ratio
                        // Center horizontally
                        left = (fabricCanvas.width - (imgWidth * scaleX)) / 2;
                    }
                    
                    // Store original dimensions and scaling for coordinate transformations
                    window.backgroundImageInfo = {
                        width: imgWidth,
                        height: imgHeight,
                        scaleX: scaleX,
                        scaleY: scaleY,
                        left: left,
                        top: top
                    };
                    
                    const fabricImage = new fabric.Image(img, {
                        left: left,
                        top: top,
                        originX: 'left',
                        originY: 'top',
                        scaleX: scaleX,
                        scaleY: scaleY
                    });
                    
                    fabricCanvas.setBackgroundImage(fabricImage, fabricCanvas.renderAll.bind(fabricCanvas));
                };
                img.src = "data:image/jpeg;base64," + data.frame_data;
            } else if (data.event === 'error') {
                console.error('Video stream error:', data.message);
            }
        };        videoStream.onclose = function(event) {
            console.log('Video stream disconnected');
            
            // Set a timer to attempt reconnection and reload configuration if needed
            if (selectedSource) {
                setTimeout(async () => {
                    try {
                        console.log(`Attempting to reconnect to video source: ${selectedSource}`);
                        const source = videoSources.find(s => s.id === selectedSource);
                        if (source) {
                            // Reconnect to the video source
                            await loadVideo(source);
                            
                            // Reload area configuration
                            await loadAreaConfiguration(selectedSource);
                        }
                    } catch (error) {
                        console.error('Error reconnecting to video source:', error);
                    }
                }, 2000); // Wait 2 seconds before attempting reconnection
            }
        };

        videoStream.onerror = function(error) {
            console.error('Video stream WebSocket error:', error);
        };

    } catch (error) {
        console.error('Error loading video:', error);
        // Use default dimensions if video load fails before getting dimensions
        if (!currentFrameWidth || !currentFrameHeight) {
             currentFrameWidth = DEFAULT_FRAME_WIDTH;
             currentFrameHeight = DEFAULT_FRAME_HEIGHT;
        }
        resizeCanvas(); // Ensure canvas is sized even on error
        showToast('Failed to load video. Check console for errors.', 'error');
    }
}

// Load existing area configuration
async function loadAreaConfiguration(sourceId) {
    try {
        console.log(`Loading area configuration for source: ${sourceId}`);
        
        const response = await fetch(`${API_ENDPOINTS.SOURCES}/${sourceId}/areas`);
        if (!response.ok) {
            throw new Error(`Failed to load area configuration: ${response.status}`);
        }
        
        const config = await response.json();
        
        if (config && config.areas) {
            console.log(`Area configuration loaded successfully. Areas found: ${Object.keys(config.areas).join(', ')}`);
            existingAreas = config.areas;
            
            // Always redraw areas after loading configuration
            redrawAreas();
            
            // Return the loaded configuration for chaining
            return config.areas;
        } else {
            console.log('No area configuration found or empty configuration');
            return {};
        }
    } catch (error) {
        console.error('Error loading area configuration:', error);
        showToast('Failed to load area configuration. Will use any locally cached areas if available.', 'warning');
        return null; // Return null to indicate error
    }
}

// Redraw existing areas on canvas
function redrawAreas() {
    // Clear canvas objects except background
    const backgroundImage = fabricCanvas.backgroundImage;
    fabricCanvas.clear(); // Clears all objects, including points and lines drawn during shape creation
    if (backgroundImage) {
        fabricCanvas.setBackgroundImage(backgroundImage, fabricCanvas.renderAll.bind(fabricCanvas));
    }

    // Ensure currentFrameWidth and currentFrameHeight are valid, otherwise use defaults
    const frameW = currentFrameWidth > 0 ? currentFrameWidth : DEFAULT_FRAME_WIDTH;
    const frameH = currentFrameHeight > 0 ? currentFrameHeight : DEFAULT_FRAME_HEIGHT;
    
    console.log(`Redrawing areas with frame dimensions: ${frameW}x${frameH}, canvas: ${fabricCanvas.width}x${fabricCanvas.height}`);

    // Draw each area by type
    for (const [areaType, areas] of Object.entries(existingAreas)) {
        areas.forEach(area => {
            if (area.points) {
                // Convert saved video coordinates to canvas coordinates for drawing
                const canvasPoints = area.points.map(p => {
                    // Improved and more accurate scaling calculation
                    const scaledX = (p[0] / frameW) * fabricCanvas.width;
                    const scaledY = (p[1] / frameH) * fabricCanvas.height;
                    console.log(`Converting video point (${p[0]}, ${p[1]}) to canvas point (${scaledX}, ${scaledY})`);
                    return {
                        x: scaledX,
                        y: scaledY
                    };
                });

                if (canvasPoints.length === 2) {
                    drawExistingLine(canvasPoints, areaType);
                } else if (canvasPoints.length >= 3) {
                    drawExistingPolygon(canvasPoints, areaType);
                }
            }
        });
    }
    fabricCanvas.renderAll(); // Ensure canvas is rendered after all updates
}

// Draw existing polygon on canvas (expects points in canvas coordinates)
function drawExistingPolygon(canvasPoints, areaType) {
    const areaColor = areaColors[areaType] || areaColors.custom;

    const polygon = new fabric.Polygon(canvasPoints, {
        stroke: areaColor.color,
        strokeWidth: 2,
        fill: areaColor.fillColor,
        selectable: false,
        hasBorders: false,
        hasControls: false,
        evented: false,
        objectType: 'area',
        areaType: areaType
    });
    fabricCanvas.add(polygon);

    // Add points as circles
    canvasPoints.forEach(point => {
        const circle = new fabric.Circle({
            left: point.x - 5,
            top: point.y - 5,
            radius: 5,
            fill: areaColor.color,
            stroke: '#fff',
            strokeWidth: 1,
            originX: 'center',
            originY: 'center',
            selectable: false,
            hasBorders: false,
            hasControls: false
        });
        fabricCanvas.add(circle);
    });
    // fabricCanvas.renderAll(); // Moved to end of redrawAreas for efficiency
}

// Draw existing line on canvas (expects points in canvas coordinates)
function drawExistingLine(canvasPoints, areaType) {
    const areaColor = areaColors[areaType] || areaColors.custom;

    const line = new fabric.Line(
        [canvasPoints[0].x, canvasPoints[0].y, canvasPoints[1].x, canvasPoints[1].y],
        {
            stroke: areaColor.color,
            strokeWidth: 2,
            selectable: false,
            hasBorders: false,
            hasControls: false,
            evented: false,
            objectType: 'area',
            areaType: areaType
        }
    );
    fabricCanvas.add(line);

    // Add points as circles
    canvasPoints.forEach(point => {
        const circle = new fabric.Circle({
            left: point.x - 5,
            top: point.y - 5,
            radius: 5,
            fill: areaColor.color,
            stroke: '#fff',
            strokeWidth: 1,
            originX: 'center',
            originY: 'center',
            selectable: false,
            hasBorders: false,
            hasControls: false
        });
        fabricCanvas.add(circle);
    });
    // fabricCanvas.renderAll(); // Moved to end of redrawAreas for efficiency
}

// Handle area type change
function onAreaTypeChange(e) {
    currentAreaType = e.target.value;
}

// Start drawing a polygon
function startDrawingPolygon() {
    if (!selectedSource) {
        alert('Please select a video source first.');
        return;
    }
    
    // Stop any existing drawing
    stopDrawing();
    
    // Start new drawing
    isDrawing = true;
    currentShape = 'polygon';
    polygonPoints = [];
    
    alert('Click on the video to add points to your polygon. Double-click to complete.');
}

// Start drawing a line
function startDrawingLine() {
    if (!selectedSource) {
        alert('Please select a video source first.');
        return;
    }
    
    // Check if selected area type supports lines
    const lineAreaTypes = ['speed', 'wrong_direction', 'traffic_line'];
    if (!lineAreaTypes.includes(currentAreaType)) {
        alert(`Line drawing is only supported for these area types: ${lineAreaTypes.join(', ')}`);
        return;
    }
    
    // Stop any existing drawing
    stopDrawing();
    
    // Start new drawing
    isDrawing = true;
    currentShape = 'line';
    linePoints = [];
    
    alert('Click on the video to set the start point, then click again to set the end point.');
}

// Stop drawing
function stopDrawing() {
    isDrawing = false;
    currentShape = null;
    
    // Remove any temporary lines
    fabricCanvas.getObjects().forEach(obj => {
        if (obj.isPreviewLine) {
            fabricCanvas.remove(obj);
        }
    });
    
    fabricCanvas.renderAll();
}

// Save the current shape (polygon or line)
function saveShape() {
    if (!selectedSource) return;

    let newArea = null;

    // Add the shape to the existing areas
    if (!existingAreas[currentAreaType]) {
        existingAreas[currentAreaType] = [];
    }

    // Ensure currentFrameWidth and currentFrameHeight are valid, otherwise use defaults
    const frameW = currentFrameWidth > 0 ? currentFrameWidth : DEFAULT_FRAME_WIDTH;
    const frameH = currentFrameHeight > 0 ? currentFrameHeight : DEFAULT_FRAME_HEIGHT;
    
    // Log the exact dimensions being used for scaling
    console.log(`Saving shape with frame dimensions: ${frameW}x${frameH}, canvas: ${fabricCanvas.width}x${fabricCanvas.height}`);    if (currentShape === 'polygon' && polygonPoints.length >= 3) {
        // Convert from canvas coordinates to original video coordinates
        const apiPoints = polygonPoints.map(point => {
            // Get background image scaling info
            const bgInfo = window.backgroundImageInfo || {
                width: frameW,
                height: frameH,
                scaleX: fabricCanvas.width / frameW,
                scaleY: fabricCanvas.height / frameH,
                left: 0,
                top: 0
            };
            
            // Adjust coordinates based on background image position and scale
            // First, account for any offset due to centering
            const adjustedX = point.x - bgInfo.left;
            const adjustedY = point.y - bgInfo.top;
            
            // Then convert from canvas scale to video coordinates
            const x = Math.round(adjustedX / bgInfo.scaleX);
            const y = Math.round(adjustedY / bgInfo.scaleY);
            
            console.log(`Converting canvas point (${point.x}, ${point.y}) → adjusted (${adjustedX}, ${adjustedY}) → video (${x}, ${y})`);
            return [x, y];
        });

        newArea = { points: apiPoints };
        existingAreas[currentAreaType].push(newArea);

        // Complete the polygon shape for display on canvas (already in canvas coordinates)
        const fabricPoints = polygonPoints.map(point => ({ x: point.x, y: point.y }));
        const polygon = new fabric.Polygon(fabricPoints, {
            stroke: areaColors[currentAreaType].color,
            strokeWidth: 2,
            fill: areaColors[currentAreaType].fillColor,
            selectable: false,
            hasBorders: false,
            hasControls: false,
            evented: false,
            objectType: 'area',
            areaType: currentAreaType
        });
        fabricCanvas.add(polygon);
        // Add circles for points
        fabricPoints.forEach(point => {
            const circle = new fabric.Circle({
                left: point.x - 5,
                top: point.y - 5,
                radius: 5,
                fill: areaColors[currentAreaType].color,
                stroke: '#fff',
                strokeWidth: 1,
                originX: 'center',
                originY: 'center',
                selectable: false,
                hasBorders: false,
                hasControls: false
            });
            fabricCanvas.add(circle);
        });    } else if (currentShape === 'line' && linePoints.length === 2) {
        // Convert from canvas coordinates to original video coordinates
        const apiPoints = linePoints.map(point => {
            // Get background image scaling info
            const bgInfo = window.backgroundImageInfo || {
                width: frameW,
                height: frameH,
                scaleX: fabricCanvas.width / frameW,
                scaleY: fabricCanvas.height / frameH,
                left: 0,
                top: 0
            };
            
            // Adjust coordinates based on background image position and scale
            // First, account for any offset due to centering
            const adjustedX = point.x - bgInfo.left;
            const adjustedY = point.y - bgInfo.top;
            
            // Then convert from canvas scale to video coordinates
            const x = Math.round(adjustedX / bgInfo.scaleX);
            const y = Math.round(adjustedY / bgInfo.scaleY);
            
            console.log(`Converting canvas point (${point.x}, ${point.y}) → adjusted (${adjustedX}, ${adjustedY}) → video (${x}, ${y})`);
            return [x, y];
        });

        newArea = { points: apiPoints };
        existingAreas[currentAreaType].push(newArea);

        // Draw the line for display on canvas (already in canvas coordinates)
        const fabricLinePoints = [
            linePoints[0].x, linePoints[0].y,
            linePoints[1].x, linePoints[1].y
        ];
        const line = new fabric.Line(fabricLinePoints, {
            stroke: areaColors[currentAreaType].color,
            strokeWidth: 2,
            selectable: false,
            hasBorders: false,
            hasControls: false,
            evented: false,
            objectType: 'area',
            areaType: currentAreaType
        });
        fabricCanvas.add(line);
        // Add circles for points
        linePoints.forEach(point => {
            const circle = new fabric.Circle({
                left: point.x - 5,
                top: point.y - 5,
                radius: 5,
                fill: areaColors[currentAreaType].color,
                stroke: '#fff',
                strokeWidth: 1,
                originX: 'center',
                originY: 'center',
                selectable: false,
                hasBorders: false,
                hasControls: false
            });
            fabricCanvas.add(circle);
        });
    }

    // Reset drawing state
    stopDrawing();
    linePoints = [];
    polygonPoints = [];

    // Redraw all areas for consistency if a new shape was added
    if (newArea) {
        redrawAreas();
    } else {
        fabricCanvas.renderAll(); // just render if no new area was fully formed
    }
}

// Clear all areas of current type
function clearAreas() {
    if (!selectedSource) {
        alert('Please select a video source first.');
        return;
    }
    
    if (confirm(`Are you sure you want to clear all ${currentAreaType} areas?`)) {
        if (existingAreas[currentAreaType]) {
            existingAreas[currentAreaType] = [];
        }
        
        redrawAreas();
    }
}

// Clear the entire canvas
function clearCanvas() {
    // Stop any existing drawing
    stopDrawing();
    
    // Clear existing areas
    existingAreas = {};
    
    // Clear canvas
    fabricCanvas.clear();
}

// Save area configuration
async function saveAreaConfiguration() {
    if (!selectedSource) {
        alert('Please select a video source first.');
        return;
    }
    
    try {
        const response = await fetch(`${API_ENDPOINTS.SOURCES}/${selectedSource}/areas`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(existingAreas)
        });
        
        if (response.ok) {
            alert('Area configuration saved successfully!');
        } else {
            const error = await response.json();
            alert(`Failed to save area configuration: ${error.detail}`);
        }
    } catch (error) {
        console.error('Error saving area configuration:', error);
        alert('Failed to save area configuration. Check console for errors.');
    }
}

// Toggle player controls
function toggleControls() {
    const controlsDiv = document.getElementById('playerControls');
    const btnToggle = document.getElementById('btnToggleControls');
    
    if (controlsDiv.style.display === 'none') {
        controlsDiv.style.display = 'flex';
        btnToggle.innerHTML = '<i class="fas fa-cog"></i> Hide Controls';
        
        // Load current model settings if available
        if (selectedSource) {
            loadModelSettings(selectedSource);
        }
    } else {
        controlsDiv.style.display = 'none';
        btnToggle.innerHTML = '<i class="fas fa-cog"></i> Show Controls';
    }
}

// Load model settings for a source
async function loadModelSettings(sourceId) {
    try {
        const response = await fetch(`${API_ENDPOINTS.SOURCES}/${sourceId}/settings`);
        if (!response.ok) {
            console.warn('Failed to load model settings, using defaults');
            return;
        }
        
        const settings = await response.json();
        
        // Update toggle switches
        if (settings.models) {
            document.getElementById('toggleAccident').checked = settings.models.accident_detection ?? true;
            document.getElementById('toggleHelmet').checked = settings.models.helmet_detection ?? true;
            document.getElementById('toggleTraffic').checked = settings.models.traffic_violation ?? true;
            document.getElementById('toggleSpeed').checked = settings.models.speed_detection ?? true;
            document.getElementById('toggleParking').checked = settings.models.parking_detection ?? true;
            document.getElementById('toggleWrongDir').checked = settings.models.wrong_direction ?? true;
        }
    } catch (error) {
        console.error('Error loading model settings:', error);
    }
}

// Save model settings
async function saveModelSettings() {
    if (!selectedSource) {
        alert('Please select a video source first.');
        return;
    }
    
    const settings = {
        models: {
            accident_detection: document.getElementById('toggleAccident').checked,
            helmet_detection: document.getElementById('toggleHelmet').checked,
            traffic_violation: document.getElementById('toggleTraffic').checked,
            speed_detection: document.getElementById('toggleSpeed').checked,
            parking_detection: document.getElementById('toggleParking').checked,
            wrong_direction: document.getElementById('toggleWrongDir').checked
        }
    };
    
    try {
        const response = await fetch(`${API_ENDPOINTS.SOURCES}/${selectedSource}/settings`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });
        
        if (response.ok) {
            showToast('Model settings applied successfully!', 'success');
        } else {
            const error = await response.json();
            showToast(`Failed to apply settings: ${error.detail}`, 'error');
        }
    } catch (error) {
        console.error('Error saving model settings:', error);
        showToast('Failed to save settings. Check console for errors.', 'error');
    }
}